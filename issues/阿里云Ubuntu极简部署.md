# BookStack 阿里云 Ubuntu 极简部署任务

## 服务器配置
- CPU: 1核
- 内存: 300MB
- 系统: Ubuntu
- 部署方式: 极简二进制部署

## 优化目标
- 移除所有前端资源
- 只保留后端API功能
- 内存占用控制在 80-120MB

## 执行计划

### 第一阶段：项目优化准备
1. ✅ 移除前端静态资源目录
2. ✅ 移除模板文件目录
3. ✅ 修改路由配置（只保留API路由）
4. ✅ 优化应用配置（生产模式，低内存）
5. ✅ 清理前端相关代码

### 第二阶段：编译构建
6. ✅ 创建编译脚本
7. ✅ 准备API专用配置
8. ✅ 创建部署脚本

### 第三阶段：服务器部署
9. ✅ 创建Ubuntu部署指南
10. ✅ 配置systemd服务
11. ✅ 内存优化建议

## 当前状态
第一和第二阶段已完成！

## 已创建的文件
- `conf/app_api_only.conf` - API专用配置
- `build_windows.bat` - Windows编译脚本
- `build_api_only.sh` - Linux编译脚本
- `Ubuntu部署指南.md` - 详细部署文档

## 优化效果
- 移除了所有前端资源（static、views目录）
- 只保留API路由
- 内存配置从64GB降至32MB
- 运行模式改为生产模式
- 预计内存占用：80-120MB
