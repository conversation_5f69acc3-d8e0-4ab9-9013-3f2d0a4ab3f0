module github.com/TruthHun/BookStack

go 1.13

replace github.com/ugorji/go/codec v0.0.0-20181204163529-d75b2dcb6bc8 => github.com/ugorji/go v1.1.7

require (
	github.com/PuerkitoBio/goquery v1.6.0
	github.com/TruthHun/converter v0.0.0-20210623150616-172c168cdcda
	github.com/TruthHun/gotil v0.0.0-20221201022546-19fe8ec6ef40
	github.com/TruthHun/html2article v0.0.0-20180202140721-67d6ff09647b
	github.com/TruthHun/html2json v0.0.0-20201228115506-dee0570dffa8
	github.com/alexcesaro/mail v0.0.0-20141015155039-29068ce49a17
	github.com/aliyun/aliyun-oss-go-sdk v2.1.0+incompatible
	github.com/andybalholm/cascadia v1.2.0 // indirect
	github.com/araddon/dateparse v0.0.0-20190622164848-0fb0a474d195
	github.com/astaxie/beego v1.12.0
	github.com/baiyubin/aliyun-sts-go-sdk v0.0.0-20180326062324-cfa1a18b161f // indirect
	github.com/boombuler/barcode v1.0.0
	github.com/dgrijalva/jwt-go v3.2.0+incompatible
	github.com/disintegration/imaging v1.6.1
	github.com/go-ego/gse v0.66.7
	github.com/go-sql-driver/mysql v1.5.0
	github.com/hashicorp/go-version v1.6.0
	github.com/kardianos/service v1.0.0
	github.com/lifei6671/gocaptcha v0.0.0-20190301083731-c467a25bc100
	github.com/mssola/user_agent v0.5.0
	github.com/nfnt/resize v0.0.0-20180221191011-83c6a9932646
	github.com/russross/blackfriday v2.0.0+incompatible
	github.com/satori/go.uuid v1.2.0 // indirect
	github.com/shiena/ansicolor v0.0.0-20200904210342-c7312218db18 // indirect
	github.com/smartystreets/goconvey v1.6.4 // indirect
	github.com/stretchr/testify v1.6.1 // indirect
	github.com/unknwon/com v1.0.1
	golang.org/x/crypto v0.0.0-20201221181555-eec23a3978ad // indirect
	golang.org/x/image v0.0.0-20190802002840-cff245a6509b // indirect
	golang.org/x/net v0.0.0-20201224014010-6772e930b67b // indirect
	golang.org/x/sys v0.0.0-20201223074533-0d417f636930 // indirect
	golang.org/x/time v0.0.0-20200416051211-89c76fbcd5d1 // indirect
	gopkg.in/asn1-ber.v1 v1.0.0-20181015200546-f715ec2f112d // indirect
	gopkg.in/ldap.v2 v2.5.1
	gopkg.in/yaml.v2 v2.4.0 // indirect
)