<!DOCTYPE html>
<html lang="zh-CN">
<head>
{{template "widgets/head.html" .}}
</head>
<body>
<div class="manual-reader manual-container manual-search-reader">
    {{template "widgets/header.html" .}}
    <div class="{{if $.IsWideScreen}}container-fluid container-widescreen{{else}}container{{end}} manual-body">
        <div class="search-head">
            <strong class="search-title">显示搜索结果为"{{.LabelName}}"的书籍</strong>
        </div>
        <div class="row">
            <div class="manual-list">
                {{range $index,$item := .Lists}}
                <div class="col-xs-6 col-sm-3 col-md-2">
                    <dl class="manual-item-standard">
                        <dt>
                            <a href="{{urlfor "DocumentController.Index" ":key" $item.Identify}}" title="{{$item.BookName}}" class="tooltips {{if gt $item.OrderIndex 0}}recommend-book{{end}}" target="_blank">
                                <img onerror="this.src='/static/images/book.png'"  src="{{showImg $item.Cover "cover"}}" class="cover img-responsive border-cover-img" alt="{{$item.BookName}}">
                            </a>
                        </dt>
                        <dd>
                            <a href="{{urlfor "DocumentController.Index" ":key" $item.Identify}}" class="name tooltips" title="{{$item.BookName}}" target="_blank">{{$item.BookName}}</a>
                        </dd>
                        {{/*<dd>*/}}
                            {{/*<span class="author">*/}}
                                {{/*<b class="text">作者</b>*/}}
                                {{/*<b class="text">-</b>*/}}
                                {{/*<b class="text">{{$item.CreateName}}</b>*/}}
                            {{/*</span>*/}}
                        {{/*</dd>*/}}
                    </dl>
                </div>
                {{end}}
                <div class="clearfix"></div>
            </div>
            <div class="pagination-container">
                {{.PageHtml}}
            </div>
        </div>
    </div>
</div>
{{template "widgets/footer.html" .}}
</body>
</html>