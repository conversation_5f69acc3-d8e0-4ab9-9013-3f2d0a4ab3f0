<header class="navbar navbar-static-top navbar-fixed-top manual-header" role="banner">
    <div class="{{if $.IsWideScreen}}container-fluid container-widescreen{{else}}container{{end}}" style="position: relative;">
        <div class="navbar-header col-sm-12 col-md-10 col-lg-10">
            <div class="btn-group dropdown-menu-right pull-right slidebar visible-xs-inline-block visible-sm-inline-block">
                <button class="btn btn-default dropdown-toggle hidden-lg" type="button" data-toggle="dropdown"><i class="fa fa-align-justify"></i></button>
                <ul class="dropdown-menu" role="menu">
                    <li>
                        <a href="/cate" class="visible-xs" title="分类"><i class="fa fa-th-list"></i> 分类</a>
                    </li>
                    <li>
                        <a href="{{urlfor "HomeController.Index"}}" class="visible-xs" title="发现"><i class="fa fa-globe"></i> 发现</a>
                    </li>
                    <li>
                        <a href="{{urlfor "LabelController.List"}}" class="visible-xs" title="标签"><i class="fa fa-tags"></i> 标签</a>
                    </li>
                    <li>
                        <a href="{{urlfor "SearchController.Search"}}" class="visible-xs visible-sm" title="搜索"><i class="fa fa-search"></i> 搜索</a>
                    </li>

                    {{if eq .CloseOpenSourceLink false}}
                        <li><a href="https://www.bookstack.cn/read/help/opensource.md" title="BookStack开源" target="_blank"><i class="fa fa-code"></i> 开源</a></li>
                    {{end}}
                    {{if .APP_PAGE}}
                        {{/*  "/app" 会自动根据后台配置的APP单页地址跳转过去  */}}
                        <li><a href="/app" title="手机APP下载" target="_blank"><i class="fa fa-paper-plane-o"></i> APP下载</a></li>
                    {{end}}
                {{if gt .Member.MemberId 0}}
                    <li>
                        <a href="{{urlfor "UserController.Index" ":username" $.Member.Account}}" title="个人主页"><i class="fa fa-home" aria-hidden="true"></i> 个人主页  {{if eq $.IsSignedToday false}}<small class="text-red">签到</small>{{end}}</a>
                    </li>
                    <li>
                        <a href="{{urlfor "SettingController.Index"}}" title="个人设置"><i class="fa fa-cogs" aria-hidden="true"></i> 个人设置</a>
                    </li>
                    <li>
                        <a href="{{urlfor "SettingController.Star"}}" title="我的收藏"><i class="fa fa-heart-o" aria-hidden="true"></i> 我的收藏</a>
                    </li>
                    {{if $.ShowCreateBookEntrance}}
                    <li>
                        <a href="{{urlfor "BookController.Index"}}" title="我的书籍"><i class="fa fa-book" aria-hidden="true"></i> 我的书籍</a>
                    </li>
                    {{end}}
                {{if eq .Member.Role 0 }}
                    <li>
                        <a href="{{urlfor "ManagerController.Index"}}" title="管理后台"><i class="fa fa-university" aria-hidden="true"></i> 管理后台</a>
                    </li>
                {{end}}
                    <li>
                        <a href="{{urlfor "AccountController.Logout"}}" title="退出登录"><i class="fa fa-sign-out"></i> 退出登录</a>
                    </li>
                {{else}}
                    <li><a href="{{urlfor "AccountController.Oauth" ":oauth" "email"}}" title="注册"><i class="fa fa-user-plus"></i> 注册</a></li>
                    <li><a href="{{urlfor "AccountController.Login"}}" title="登录"><i class="fa fa-sign-in"></i> 登录</a></li>
                {{end}}
                </ul>
            </div>

            <a href="/" class="navbar-brand" title="{{.SITE_NAME}}">
                <img class="logo" src="/static/images/logo.png" alt="{{.SITE_NAME}}">
            </a>
            <nav class="collapse navbar-collapse">

                <ul class="nav navbar-nav">
                    <li class="{{if .IsCate}}active{{end}}">
                        <a href="/cate" title="分类"><i class="fa fa-th-list"></i> 分类</a>
                    </li>
                    <li {{if .IsHome}}class="active"{{end}}>
                        <a href="{{urlfor "HomeController.Index" }}" title="发现"><i class="fa fa-globe"></i> 发现</a>
                    </li>
                    <li {{if .IsRank}}class="active"{{end}}>
                        <a href="{{urlfor "RankController.Index"}}" title="榜单"><img src="/static/images/rank.png" alt=""> 榜单</a>
                    </li>
                    {{if eq .HideTag false}}
                    <li class="{{if .IsLabel}}active{{end}}">
                        <a href="{{urlfor "LabelController.List" }}" class="hidden-sm" title="标签"><i class="fa fa-tags"></i> 标签</a>
                    </li>
                    {{end}}
                    {{if eq .CloseSubmitEnter false}}
                    <li class="{{if .IsSubmit}}active{{end}}">
                        <a href="{{urlfor "SubmitController.Index" }}" class="hidden-sm" title="收录"><i class="fa fa-check-square-o"></i> 收录</a>
                    </li>
                    {{end}}
                    {{if eq .CloseOpenSourceLink false}}
                    <li class="hidden-sm"><a href="https://www.bookstack.cn/read/help/opensource.md" title="BookStack开源" target="_blank"><i class="fa fa-code"></i> 开源</a></li>
                    {{end}}
                    {{if .APP_PAGE}}
                        {{/*  "/app" 会自动根据后台配置的APP单页地址跳转过去  */}}
                        <li><a href="/app" title="手机APP下载" target="_blank"><i class="fa fa-paper-plane-o"></i> APP下载</a></li>
                    {{end}}
                    {{if .IsSearch}}
                        <li class="active">
                            <a href="{{urlfor "SearchController.Search" }}" title="搜索"><i class="fa fa-search"></i> 搜索</a>
                        </li>
                    {{end}}
                </ul>

                {{if .IsSearch}}{{else}}
                    <div class="searchbar pull-left">
                        <form class="form-inline" action="{{urlfor "SearchController.Result"}}" method="get">
                            <input class="form-control" name="wd" type="search"  placeholder="书籍搜索..." value="{{.Keyword}}">
                            <button class="search-btn">
                                <i class="fa fa-search"></i>
                            </button>
                        </form>
                    </div>
                {{end}}


            </nav>
        </div>
        <nav class="navbar-collapse hidden-xs hidden-sm" style="position: absolute;right: 0px;" role="navigation">
            <ul class="nav navbar-nav navbar-right" style="margin-right: 15px;">
                {{if gt .Member.MemberId 0}}
                <li>
                    <div class="img user-info" data-toggle="dropdown">
                        <img  onerror="this.src='/static/images/avatar.png'"  src="{{showImg .Member.Avatar "avatar"}}" class="img-circle userbar-avatar border" alt="{{.Member.Nickname}}">
                        <div class="userbar-content">
                            <span>{{.Member.Nickname}}</span>
                            <div>{{.Member.RoleName}}</div>
                        </div>
                        <i class="fa fa-chevron-down" aria-hidden="true"></i>
                    </div>
                    <ul class="dropdown-menu user-info-dropdown" role="menu">
                        <li>
                            <a href="{{urlfor "UserController.Index" ":username" $.Member.Account}}" title="个人主页"><i class="fa fa-home" aria-hidden="true"></i> 个人主页  {{if eq $.IsSignedToday false}}<small class="text-red">签到</small>{{end}}</a>
                        </li>
                        <li>
                            <a href="{{urlfor "SettingController.Index"}}" title="个人设置"><i class="fa fa-cogs" aria-hidden="true"></i> 个人设置</a>
                        </li>
                        <li>
                            <a href="{{urlfor "SettingController.Star"}}" title="我的收藏"><i class="fa fa-heart-o" aria-hidden="true"></i> 我的收藏</a>
                        </li>
                        {{if $.ShowCreateBookEntrance}}
                        <li>
                            <a href="{{urlfor "BookController.Index"}}" title="我的书籍"><i class="fa fa-book" aria-hidden="true"></i> 我的书籍</a>
                        </li>
                        {{end}}
                        {{if eq .Member.Role 0  1}}
                        <li>
                            <a href="{{urlfor "ManagerController.Index"}}" title="管理后台"><i class="fa fa-university" aria-hidden="true"></i> 管理后台</a>
                        </li>
                        {{end}}
                        <li>
                            <a href="{{urlfor "AccountController.Logout"}}" title="退出登录"><i class="fa fa-sign-out"></i> 退出登录</a>
                        </li>
                    </ul>
                </li>
                {{else}}
                    <li><a href="{{urlfor "AccountController.Oauth" ":oauth" "email"}}" title="注册"><i class="fa fa-user-plus"></i> 注册</a></li>
                    <li><a href="{{urlfor "AccountController.Login"}}" title="登录"><i class="fa fa-sign-in"></i> 登录</a></li>
                {{end}}
            </ul>
        </nav>


    </div>
</header>