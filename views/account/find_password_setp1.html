<!DOCTYPE html>
<html lang="zh-cn">
<head>
    {{template "widgets/head.html" .}}
</head>
<body class="manual-container">
<header class="navbar navbar-static-top smart-nav navbar-fixed-top manual-header" role="banner">
    <div class="{{if $.IsWideScreen}}container-fluid container-widescreen{{else}}container{{end}}">
        <div class="navbar-header col-sm-12 col-md-6 col-lg-5">
            <a href="/" class="navbar-brand">
                <img class="logo" src="/static/images/logo.png" alt="{{.SITE_NAME}}">
            </a>
        </div>
    </div>
</header>
<div class="{{if $.IsWideScreen}}container-fluid container-widescreen{{else}}container{{end}} manual-body">
    <div class="row login">
        <div class="login-body">
            <form role="form" method="post" id="findPasswordForm">
                <h3 class="text-center">找回密码</h3>
                <div class="form-group">
                    <div class="input-group">
                        <div class="input-group-addon">
                            <i class="fa fa-at"></i>
                        </div>
                        <input type="text" class="form-control" placeholder="邮箱" name="email" id="email" autocomplete="off">
                    </div>
                </div>
                <div class="form-group">
                    <div class="input-group" style="float: left;width: 195px;">
                        <div class="input-group-addon">
                            <i class="fa fa-check-square"></i>
                        </div>
                        <input type="text" name="captcha" id="code" class="form-control" style="width: 150px" maxlength="6" placeholder="验证码" autocomplete="off">&nbsp;
                    </div>
                    {{create_captcha}}
                    <div class="clearfix"></div>
                </div>

                <div class="form-group">
                    <button type="submit" id="btnSendMail" class="btn btn-success" style="width: 100%"  data-loading-text="正在处理..." autocomplete="off">找回密码</button>
                </div>

            </form>
        </div>
    </div>
    <div class="clearfix"></div>
</div>
{{template "widgets/footer.html" .}}
<!-- Include all compiled plugins (below), or include individual files as needed -->
<script src="{{$.StaticDomain}}/static/layer/layer.js" type="text/javascript"></script>
<script src="{{$.StaticDomain}}/static/js/jquery.form.js" type="text/javascript"></script>
<script type="text/javascript">
    $(function () {
        $("#email,#code").on('focus',function () {
            $(this).tooltip('destroy').parents('.form-group').removeClass('has-error');;
        });

        $(document).keydown(function (e) {
            var event = document.all ? window.event : e;
            if(event.keyCode == 13){
                $("#btn-login").click();
            }
        });

        $("#findPasswordForm").ajaxForm({
            beforeSubmit : function () {
                var $btn = $(this).button('loading');

                var email = $.trim($("#email").val());
                if(email === ""){
                    $("#email").tooltip({placement:"auto",title : "邮箱不能为空",trigger : 'manual'})
                        .tooltip('show')
                        .parents('.form-group').addClass('has-error');
                    $btn.button('reset');
                    return false;

                }
                var code = $.trim($("#code").val());
                if(code === ""){
                    $("#code").tooltip({title : '验证码不能为空',trigger : 'manual'})
                        .tooltip('show')
                        .parents('.form-group').addClass('has-error');
                    $btn.button('reset');
                    return false;
                }
                $("#btnSendMail").button("loading");
            },
            success : function (res) {

                if(res.errcode !== 0){
                    $("#captcha-img").click();
                    $("#code").val('');
                    layer.msg(res.message);
                    $("#btnSendMail").button('reset');
                }else{
                    alert("邮件发送成功，请登录邮箱查看。")
                    window.location = res.data;
                }
            },
            error :function () {
                $("#captcha-img").click();
                $("#code").val('');
                layer.msg('系统错误');
                $("#btnSendMail").button('reset');
            }
        });

    });
</script>
</body>
</html>