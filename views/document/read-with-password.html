<!DOCTYPE html>
<html lang="zh-CN">
<head>
    {{template "widgets/head.html" .}}
        <style>
            .relate-bookname{display: block;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;}
            h4{font-weight: normal;font-size: 16px;color: #333;}
        </style>
</head>
<body id="bookstack-intro">
{{template "widgets/header.html" .}}
<div class="{{if $.IsWideScreen}}container-fluid container-widescreen{{else}}container{{end}}">
    <div class="row bookstack-info">
        <div class="col-xs-12 col-sm-6 col-sm-offset-3">
            <form role="form" style="padding: 180px 0" method="post" action="{{urlfor "DocumentController.Index" ":key" .Identify}}">
                    <div class="input-group input-group-lg">
                        <input type="password" name="token" placeholder="请输入书籍访问密码" class="form-control">
                        <span class="input-group-addon visit-submit" style="cursor: pointer">立即访问</span>
                    </div>
                {{if .ShowErrTips}}
                <div class="help-block text-center">
                    <div class="text-danger" style="margin-top: 30px">访问密码不正确</div>
                </div>
                {{end}}
            </form>
        </div>
    </div>
</div>
{{template "widgets/footer.html" .}}
<script type="text/javascript" src="{{$.StaticDomain}}/static/js/toast.script.js"></script>
<script type="text/javascript" src="/static/js/main.js?version={{$.Version}}"></script>
<script>
    $(function(){
        $(".visit-submit").click(function () {
            $(this).parents("form").submit();
        })
    })
</script>
</body>
</html>