<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">

    <title>友链管理 - {{.SITE_NAME}}</title>

    <link href="/static/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/font-awesome/css/font-awesome.min.css" rel="stylesheet">

    <link href="/static/css/main.css?version={{$.Version}}" rel="stylesheet">
{{/*<script src="/static/html5shiv/3.7.3/html5shiv.min.js"></script>*/}}
    <script src="/static/html5shiv/3.7.3/html5shiv.min.js"></script>
{{/*<script src="/static/respond.js/1.4.2/respond.min.js"></script>*/}}
    <script src="/static/respond.js/1.4.2/respond.min.js"></script>

    <link rel="stylesheet" href="/static/css/toast.css">
</head>
<body>
<div class="manual-reader">
    {{template "widgets/header.html" .}}
    <div class="{{if $.IsWideScreen}}container-fluid container-widescreen{{else}}container{{end}} manual-body">
        <div class="row">
            <div class="page-left">
                {{template "manager/menu.html" .}}
            </div>
            <div class="page-right">
                <div class="m-box">
                    <div class="box-head">
                        <strong class="box-title">友链管理</strong>
                        <a href="JavaScript:void(0);" class="pull-right btn btn-success" data-toggle="modal" data-target="#FlModal">新增友链</a>
                    </div>
                </div>
                <div class="box-body" id="bookList">
                    <table class="table table-hover table-striped">
                        <caption>说明：排序，数值越小越靠前</caption>
                        <thead>
                        <tr>
                            <th>名称</th>
                            <th>链接</th>
                            <th>备注</th>
                            <th>排序</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                        </thead>
                        <tbody>
                            {{range .Links}}
                                <tr>
                                    <td>
                                        <input type="text" data-url="{{urlfor "ManagerController.UpdateFriendlink"}}?id={{.Id}}" class="form-control change-update" name="title" value="{{.Title}}">
                                    </td>
                                    <td>
                                        <input type="text" data-url="{{urlfor "ManagerController.UpdateFriendlink"}}?id={{.Id}}" class="form-control change-update" name="link" value="{{.Link}}">
                                    </td>
                                    <td>
                                        <input type="text" data-url="{{urlfor "ManagerController.UpdateFriendlink"}}?id={{.Id}}" class="form-control change-update" name="remark" value="{{.Remark}}">
                                    </td>
                                    <td>
                                        <input type="number" data-url="{{urlfor "ManagerController.UpdateFriendlink"}}?id={{.Id}}" class="form-control change-update" name="sort" value="{{.Sort}}">
                                    </td>
                                    <td>
                                        {{if .Status}}
                                            <span class="text-success">启用</span>
                                        {{else}}
                                            <span class="text-danger">关闭</span>
                                        {{end}}
                                    </td>
                                    <td>
                                        {{if .Status}}
                                            <a href="{{urlfor "ManagerController.UpdateFriendlink"}}?id={{.Id}}&field=status&value=0" class="ajax-get">关闭</a>
                                        {{else}}
                                            <a href="{{urlfor "ManagerController.UpdateFriendlink"}}?id={{.Id}}&field=status&value=1" class="ajax-get">启用</a>
                                        {{end}}
                                        <a href="{{urlfor "ManagerController.DelFriendlink"}}?id={{.Id}}" class="text-danger confirm ajax-get">删除</a>
                                    </td>
                                </tr>
                            {{end}}
                        </tbody>
                    </table>

                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="FlModal" tabindex="-1" role="dialog" aria-labelledby="FlModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{urlfor "ManagerController.AddFriendlink"}}" class="ajax-form" method="post">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                    <h4 class="modal-title" id="FlModalLabel">新增友链</h4>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label>名称</label>
                        <input type="text" class="form-control" name="title">
                    </div>
                    <div class="form-group">
                        <label>链接</label>
                        <input type="text" class="form-control" name="link">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消新增</button>
                    <button type="submit" class="btn btn-success">新增友链</button>
                </div>
            </form>
        </div>
    </div>
</div>


{{/*<script src="/static/jquery/1.12.4/jquery.min.js" type="text/javascript"></script>*/}}
<script src="/static/jquery/1.12.4/jquery.min.js" type="text/javascript"></script>
{{/*<script src="/static/bootstrap/js/bootstrap.min.js" type="text/javascript"></script>*/}}
<script src="/static/bootstrap/js/bootstrap.min.js" type="text/javascript"></script>
<script src="{{$.StaticDomain}}/static/js/toast.script.js"></script>
<script src="/static/js/main.js?version={{$.Version}}" type="text/javascript"></script>
</body>
</html>