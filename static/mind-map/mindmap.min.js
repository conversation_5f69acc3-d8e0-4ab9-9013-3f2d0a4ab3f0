//base64转换为图片blob
function dataURLtoBlob(dataurl) {
	 var arr = dataurl.split(',');
	  //注意base64的最后面中括号和引号是不转译的   
	  var _arr = arr[1].substring(0,arr[1].length-2);
	  var mime = arr[0].match(/:(.*?);/)[1],
		  bstr =atob(_arr),
		  n = bstr.length,
		  u8arr = new Uint8Array(n);
	  while (n--) {
		  u8arr[n] = bstr.charCodeAt(n);
	  }
	  return new Blob([u8arr], {
		  type: mime
	  });
  }

// 将 li 节点转换为 JSON 数据
function li2jsonData(liNode) {
    var liData;
    var aNode = liNode.children("a:first");
    if (aNode.length !== 0) {
        liData = {
            "data": {
                "text": aNode.text(),
                "hyperlink": aNode.attr("href")
            }
        };
    } else {
        liData = {
            "data": {
                "text": liNode[0].childNodes[0].nodeValue.trim()
            }
        };
    }

    liNode.find("> ul > li").each(function () {
        if (!liData.hasOwnProperty("children")) {
            liData.children = [];
        }
        liData.children.push(li2jsonData($(this)));
    });

    return liData;
}

function drawMindMap(div) {
    //各参数解析开始
    var lang = $(div).find(".mindmapoption").text();
	var sizeps = lang.match(/size:(mindmap-sm|mindmap-md|mindmap-lg)(?= )/i);
	var Templateps = lang.match(/Template:(fresh-blue|filetree|fish-bone|right|structure|tianpan)(?= )/i);
	var Themeps = lang.match(/Theme:(classic|classic-compact|fish|fresh-blue|fresh-blue-compat|fresh-green|fresh-green-compat|fresh-pink|fresh-pink-compat|fresh-purple|fresh-purple-compat|fresh-red|fresh-red-compat|fresh-soil|fresh-soil-compat|snow|snow-compact|tianpan|tianpan-compact|wire)(?= )/i);
	var protocolps=lang.match(/protocol:(json|text|markdown|list)(?= )/i);
	var tmpshowps=lang.match(/tmpshow:(true)(?= )/i);
	var size=(sizeps!== null)?sizeps[1]:"mindmap-md";
	var Theme=(Themeps!== null)?Themeps[1]:"fresh-blue";
	var protocol=(protocolps!== null)?protocolps[1]:"markdown";
	var Template=(Templateps!== null)?Templateps[1]:"default";
	var tmpshow=(tmpshowps!== null)?"":"style=\"display:none;\"";
    //参数解析结束
    var markdownText = $(div).find(".mindmaptmp").text().trim();
    if (protocol == "list") {
        var ulElement = $(div).find(".mindmaptmp").find(">ul:first");
        var mmData = {
            "root": {}
        };
        var minder = new kityminder.Minder({
                renderTo: div
            });
        try {
            mmData.root = li2jsonData(ulElement.children("li:first"));
            mmData.template = Template;
            mmData.theme = Theme;
            minder.importData('json', JSON.stringify(mmData));
            minder.disable();
            /* 		minder.setTemplate(Template);
            minder.setTheme(Theme);	 */
            minder.execCommand('hand');
        } catch (e) {
            console.log(e);
        }

        $(ulElement).hide();
    } else {
        var minder = new kityminder.Minder({
                renderTo: div
            });
        var markdownText = $(div).find(".mindmaptmp").text().trim();
        try {
            minder.importData('json', markdownText);
            minder.disable();
            //以下两句，在运行时没什么效果，包含useTemplate
            /*minder.setTemplate(Template);
            minder.setTheme(Theme); */
			//绑定双击事件，当双击空白处时，脑图由抓手和选区间切换，当双击节点时，显示备注。
			minder.on('dblclick',function() {
				var node = minder.getSelectedNode();
				if (node) {
					var tt = node.getData().note;
					if(tt){
						var note = $("#ModalNote").find(".modal-body");
						$("#ModalNote").find(".modal-header h1").text("【"+node.getText()+"】备注");
						//建议备注的这里使用markdown语法编写，再调用marked.js解析如无需解析的，可使用
						//note.html(tt);
						//最好是加参数来控制这里要不要解析
						//正解应用note.html(editormd.$marked(tt));由于该对话框缺少顶层div，解析后的css一样不能正确引用，为了避免前台去加载editor.md.js来解析，减少风险这里简单粗爆的解析一下就好。
						note.html(marked(tt));
						$("#ModalNote").modal("show");
					}
				}else {
					var note = $("#ModalNote").find(".modal-body");
					note.html("");					
					minder.execCommand('hand');
				}
			});
			//定义滚轮事件，实现缩放
			minder.on('mousewheel DOMMouseScroll',function(e){
				e = e || window.event;  
				if (e.originEvent.wheelDelta) {  //判断浏览器IE，谷歌滑轮事件，测试通过              
					if (e.originEvent.wheelDelta > 0) { //当滑轮向上滚动时  
						minder.execCommand('ZoomIn');  
					}  
					if (e.originEvent.wheelDelta < 0) { //当滑轮向下滚动时  
						minder.execCommand('ZoomOut'); 
					}  
				} else if (e.originEvent.detail) {  //Firefox滑轮事件，未经测试  
					if (e.originEvent.detail> 0) { //当滑轮向上滚动时  
						minder.execCommand('ZoomIn'); 
					}  
					if (e.originEvent.detail< 0) { //当滑轮向下滚动时  
					minder.execCommand('ZoomOut');  
					}  
				}  
			});
			//定义右键导出,经测试，导出为png格式最佳，至余其余四种格式，觉得意义不大，（默认内置五种数据协议 `json`、`text`、`markdown`、`svg` 和 `png`）
			/* 
			一、个人认为该段最佳解为，定义右键菜单，提供上述5种格式供用户选择；
			二、对不同的浏览器进行判断；
			三、当存入后台时，建议存入png图片。
			参考：https://blog.csdn.net/u012615439/article/details/91411642
			*/
			minder.on('mousedown',function(e) {
				if (e.originEvent.which == 3) //如果是右键
				{
					minder.exportData('png').then(function(content) {
						var blob = dataURLtoBlob(content);//将base64编码转换为blob对象
						var a = document.createElement("a");//建立标签，模拟点击下载
						a.download = "思维导图.png";//这里的名字感觉定义为根节点名称要好一些
						a.href = URL.createObjectURL(blob);
						a.click();
        })
    }
});
			//解析完成后，将选区置为抓手状态
            minder.execCommand('hand');
        } catch (e) {
            console.log(e);
        }

    }
}
