<!doctype html>

<title>CodeMirror: COBOL mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<link rel="stylesheet" href="../../theme/neat.css">
<link rel="stylesheet" href="../../theme/elegant.css">
<link rel="stylesheet" href="../../theme/erlang-dark.css">
<link rel="stylesheet" href="../../theme/night.css">
<link rel="stylesheet" href="../../theme/monokai.css">
<link rel="stylesheet" href="../../theme/cobalt.css">
<link rel="stylesheet" href="../../theme/eclipse.css">
<link rel="stylesheet" href="../../theme/rubyblue.css">
<link rel="stylesheet" href="../../theme/lesser-dark.css">
<link rel="stylesheet" href="../../theme/xq-dark.css">
<link rel="stylesheet" href="../../theme/xq-light.css">
<link rel="stylesheet" href="../../theme/ambiance.css">
<link rel="stylesheet" href="../../theme/blackboard.css">
<link rel="stylesheet" href="../../theme/vibrant-ink.css">
<link rel="stylesheet" href="../../theme/solarized.css">
<link rel="stylesheet" href="../../theme/twilight.css">
<link rel="stylesheet" href="../../theme/midnight.css">
<link rel="stylesheet" href="../../addon/dialog/dialog.css">
<script src="../../lib/codemirror.js"></script>
<script src="../../addon/edit/matchbrackets.js"></script>
<script src="cobol.js"></script>
<script src="../../addon/selection/active-line.js"></script>
<script src="../../addon/search/search.js"></script>
<script src="../../addon/dialog/dialog.js"></script>
<script src="../../addon/search/searchcursor.js"></script>
<style>
        .CodeMirror {
          border: 1px solid #eee;
          font-size : 20px;
          height : auto !important;
        }
        .CodeMirror-activeline-background {background: #555555 !important;}
    </style>
<div id=nav>
  <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png"></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">COBOL</a>
  </ul>
</div>

<article>
<h2>COBOL mode</h2>

    <p> Select Theme <select onchange="selectTheme()" id="selectTheme">
        <option>default</option>
        <option>ambiance</option>
        <option>blackboard</option>
        <option>cobalt</option>
        <option>eclipse</option>
        <option>elegant</option>
        <option>erlang-dark</option>
        <option>lesser-dark</option>
        <option>midnight</option>
        <option>monokai</option>
        <option>neat</option>
        <option>night</option>
        <option>rubyblue</option>
        <option>solarized dark</option>
        <option>solarized light</option>
        <option selected>twilight</option>
        <option>vibrant-ink</option>
        <option>xq-dark</option>
        <option>xq-light</option>
    </select>    Select Font Size <select onchange="selectFontsize()" id="selectFontSize">
          <option value="13px">13px</option>
          <option value="14px">14px</option>
          <option value="16px">16px</option>
          <option value="18px">18px</option>
          <option value="20px" selected="selected">20px</option>
          <option value="24px">24px</option>
          <option value="26px">26px</option>
          <option value="28px">28px</option>
          <option value="30px">30px</option>
          <option value="32px">32px</option>
          <option value="34px">34px</option>
          <option value="36px">36px</option>
        </select>
<label for="checkBoxReadOnly">Read-only</label>
<input type="checkbox" id="checkBoxReadOnly" onchange="selectReadOnly()">
<label for="id_tabToIndentSpace">Insert Spaces on Tab</label>
<input type="checkbox" id="id_tabToIndentSpace" onchange="tabToIndentSpace()">
</p>
<textarea id="code" name="code">
---------1---------2---------3---------4---------5---------6---------7---------8
12345678911234567892123456789312345678941234567895123456789612345678971234567898
000010 IDENTIFICATION DIVISION.                                        MODTGHERE
000020 PROGRAM-ID.       SAMPLE.
000030 AUTHOR.           TEST SAM. 
000040 DATE-WRITTEN.     5 February 2013
000041
000042* A sample program just to show the form.
000043* The program copies its input to the output,
000044* and counts the number of records.
000045* At the end this number is printed.
000046
000050 ENVIRONMENT DIVISION.
000060 INPUT-OUTPUT SECTION.
000070 FILE-CONTROL.
000080     SELECT STUDENT-FILE     ASSIGN TO SYSIN
000090         ORGANIZATION IS LINE SEQUENTIAL.
000100     SELECT PRINT-FILE       ASSIGN TO SYSOUT
000110         ORGANIZATION IS LINE SEQUENTIAL.
000120
000130 DATA DIVISION.
000140 FILE SECTION.
000150 FD  STUDENT-FILE
000160     RECORD CONTAINS 43 CHARACTERS
000170     DATA RECORD IS STUDENT-IN.
000180 01  STUDENT-IN              PIC X(43).
000190
000200 FD  PRINT-FILE
000210     RECORD CONTAINS 80 CHARACTERS
000220     DATA RECORD IS PRINT-LINE.
000230 01  PRINT-LINE              PIC X(80).
000240
000250 WORKING-STORAGE SECTION.
000260 01  DATA-REMAINS-SWITCH     PIC X(2)      VALUE SPACES.
000261 01  RECORDS-WRITTEN         PIC 99.
000270
000280 01  DETAIL-LINE.
000290     05  FILLER              PIC X(7)      VALUE SPACES.
000300     05  RECORD-IMAGE        PIC X(43).
000310     05  FILLER              PIC X(30)     VALUE SPACES.
000311 
000312 01  SUMMARY-LINE.
000313     05  FILLER              PIC X(7)      VALUE SPACES.
000314     05  TOTAL-READ          PIC 99.
000315     05  FILLER              PIC X         VALUE SPACE.
000316     05  FILLER              PIC X(17)     
000317                 VALUE  'Records were read'.
000318     05  FILLER              PIC X(53)     VALUE SPACES.
000319
000320 PROCEDURE DIVISION.
000321
000330 PREPARE-SENIOR-REPORT.
000340     OPEN INPUT  STUDENT-FILE
000350          OUTPUT PRINT-FILE.
000351     MOVE ZERO TO RECORDS-WRITTEN.
000360     READ STUDENT-FILE
000370         AT END MOVE 'NO' TO DATA-REMAINS-SWITCH
000380     END-READ.
000390     PERFORM PROCESS-RECORDS
000410         UNTIL DATA-REMAINS-SWITCH = 'NO'.
000411     PERFORM PRINT-SUMMARY.
000420     CLOSE STUDENT-FILE
000430           PRINT-FILE.
000440     STOP RUN.
000450
000460 PROCESS-RECORDS.
000470     MOVE STUDENT-IN TO RECORD-IMAGE.
000480     MOVE DETAIL-LINE TO PRINT-LINE.
000490     WRITE PRINT-LINE.
000500     ADD 1 TO RECORDS-WRITTEN.
000510     READ STUDENT-FILE
000520         AT END MOVE 'NO' TO DATA-REMAINS-SWITCH
000530     END-READ. 
000540
000550 PRINT-SUMMARY.
000560     MOVE RECORDS-WRITTEN TO TOTAL-READ.
000570     MOVE SUMMARY-LINE TO PRINT-LINE.
000571     WRITE PRINT-LINE. 
000572
000580
</textarea>
    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        lineNumbers: true,
        matchBrackets: true,
        mode: "text/x-cobol",
        theme : "twilight",
        styleActiveLine: true,
        showCursorWhenSelecting : true,  
      });
      function selectTheme() {
        var themeInput = document.getElementById("selectTheme");
        var theme = themeInput.options[themeInput.selectedIndex].innerHTML;
        editor.setOption("theme", theme);
      }
      function selectFontsize() {
        var fontSizeInput = document.getElementById("selectFontSize");
        var fontSize = fontSizeInput.options[fontSizeInput.selectedIndex].innerHTML;
        editor.getWrapperElement().style.fontSize = fontSize;
        editor.refresh();
      }
      function selectReadOnly() {
        editor.setOption("readOnly", document.getElementById("checkBoxReadOnly").checked);
      }
      function tabToIndentSpace() {
        if (document.getElementById("id_tabToIndentSpace").checked) {
            editor.setOption("extraKeys", {Tab: function(cm) { cm.replaceSelection("    ", "end"); }});
        } else {
            editor.setOption("extraKeys", {Tab: function(cm) { cm.replaceSelection("    ", "end"); }});
        }
      }
    </script>
  </article>
