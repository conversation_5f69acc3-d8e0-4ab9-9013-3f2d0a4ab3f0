<!doctype html>
<html>
  <head>
    <title>CodeMirror: EBNF Mode</title>
    <meta charset="utf-8"/>
    <link rel=stylesheet href="../../doc/docs.css">

    <link rel="stylesheet" href="../../lib/codemirror.css">
    <script src="../../lib/codemirror.js"></script>
    <script src="../javascript/javascript.js"></script>
    <script src="ebnf.js"></script>
    <style type="text/css">.CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}</style>
  </head>
  <body>
    <div id=nav>
      <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png"></a>

      <ul>
        <li><a href="../../index.html">Home</a>
        <li><a href="../../doc/manual.html">Manual</a>
        <li><a href="https://github.com/codemirror/codemirror">Code</a>
      </ul>
      <ul>
        <li><a href="../index.html">Language modes</a>
        <li><a class=active href="#">EBNF Mode</a>
      </ul>
    </div>

    <article>
      <h2>EBNF Mode (bracesMode setting = "javascript")</h2>
      <form><textarea id="code" name="code">
/* description: Parses end executes mathematical expressions. */

/* lexical grammar */
%lex

%%
\s+                   /* skip whitespace */
[0-9]+("."[0-9]+)?\b  return 'NUMBER';
"*"                   return '*';
"/"                   return '/';
"-"                   return '-';
"+"                   return '+';
"^"                   return '^';
"("                   return '(';
")"                   return ')';
"PI"                  return 'PI';
"E"                   return 'E';
&lt;&lt;EOF&gt;&gt;               return 'EOF';

/lex

/* operator associations and precedence */

%left '+' '-'
%left '*' '/'
%left '^'
%left UMINUS

%start expressions

%% /* language grammar */

expressions
: e EOF
{print($1); return $1;}
;

e
: e '+' e
{$$ = $1+$3;}
| e '-' e
{$$ = $1-$3;}
| e '*' e
{$$ = $1*$3;}
| e '/' e
{$$ = $1/$3;}
| e '^' e
{$$ = Math.pow($1, $3);}
| '-' e %prec UMINUS
{$$ = -$2;}
| '(' e ')'
{$$ = $2;}
| NUMBER
{$$ = Number(yytext);}
| E
{$$ = Math.E;}
| PI
{$$ = Math.PI;}
;</textarea></form>
      <script>
        var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
          mode: {name: "ebnf"},
          lineNumbers: true,
          bracesMode: 'javascript'
        });
      </script>
      <h3>The EBNF Mode</h3>
      <p> Created by <a href="https://github.com/robertleeplummerjr">Robert Plummer</a></p>
    </article>
  </body>
</html>
